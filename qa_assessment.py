import os
import sys
import openai
import json
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# OpenAI configuration
openai_api_key = os.getenv('OPENAI_API_KEY')
assistant_id = os.getenv('OPENAI_ASSISTANT_ID')

# Validate required environment variables
required_vars = {
    'OPENAI_API_KEY': openai_api_key,
    'OPENAI_ASSISTANT_ID': assistant_id
}

missing_vars = [var for var, value in required_vars.items() if not value]
if missing_vars:
    print(f"Error: Missing required environment variables: {', '.join(missing_vars)}")
    print("Please check your .env file and ensure all required variables are set.")
    sys.exit(1)

# Set OpenAI API key
openai.api_key = openai_api_key

def assess_call(transcript):
    # Send the transcript to OpenAI for assessment using the Assistant ID
    response = openai.Assistant.create_completion(
        assistant_id=assistant_id,
        messages=[
            {"role": "user", "content": transcript}
        ]
    )

    # Extract the response from OpenAI
    result = response.choices[0].message.content.strip()
    return result
import openai
import json

# OpenAI configuration
openai.api_key = "********************************************************************************************************************************************************************"
assistant_id = "asst_mu8uV2VDj2eIChHHdUEZtIar"  # Replace with your Assistant ID

def assess_call(transcript):
    # Send the transcript to OpenAI for assessment using the Assistant ID
    response = openai.Assistant.create_completion(
        assistant_id=assistant_id,
        messages=[
            {"role": "user", "content": transcript}
        ]
    )

    # Extract the response from OpenAI
    result = response.choices[0].message.content.strip()
    return result
# Call Transcript QA Analysis Pipeline

## Overview

This project is an end-to-end data processing pipeline that automates the analysis of call transcripts using AI-powered quality assurance insights. The system fetches call transcripts from MongoDB, processes them through OpenAI Assistant for intelligent QA analysis, and stores the processed results in Pinecone vector database for efficient retrieval and search capabilities.

## 🚀 Features

- **Automated Transcript Processing**: Fetches call transcripts from MongoDB collections
- **AI-Powered QA Analysis**: Leverages OpenAI Assistant for intelligent quality assurance insights
- **Parallel Processing**: Multi-threaded architecture for efficient processing of large datasets
- **Vector Storage**: Stores processed results in Pinecone for semantic search and retrieval
- **Error Handling**: Robust error handling and logging for production reliability
- **Scalable Architecture**: Queue-based processing system that can handle varying workloads

## 🛠️ Technology Stack

- **Python 3.x**: Core programming language
- **MongoDB**: Source database for call transcripts
- **OpenAI API**: AI-powered transcript analysis using Assistant API
- **Pinecone**: Vector database for storing processed results and embeddings
- **Threading**: Concurrent processing using ThreadPoolExecutor
- **Flask**: Web framework (included in dependencies)

## 📋 Prerequisites

Before running this application, ensure you have:

1. **Python 3.x** installed on your system
2. **MongoDB** access with call transcript data
3. **OpenAI API** account and API key
4. **Pinecone** account and API key
5. **Virtual environment** (recommended)

## 🔧 Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd project-02
```

### 2. Set Up Virtual Environment
```bash
# Create virtual environment
python -m venv myenv

# Activate virtual environment
# On Windows:
myenv\Scripts\activate
# On macOS/Linux:
source myenv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

## ⚙️ Configuration

### 1. MongoDB Configuration
Update the MongoDB connection details in `utils.py`:

```python
mongo_uri = "your_mongodb_connection_string"
mongo_db_name = "your_database_name"
mongo_collection_name = "your_collection_name"
```

### 2. OpenAI Configuration
Update the OpenAI settings in `qa_assessment.py`:

```python
openai.api_key = "your_openai_api_key"
assistant_id = "your_assistant_id"
```

### 3. Pinecone Configuration
Update the Pinecone settings in `utils.py`:

```python
pinecone_api_key = "your_pinecone_api_key"
pinecone_environment = "your_pinecone_environment"
pinecone_index_name = "your_index_name"
```

## 🚀 Usage

### Running the Application

1. **Activate Virtual Environment**:
   ```bash
   myenv\Scripts\activate
   ```

2. **Run the Main Script**:
   ```bash
   python main.py
   ```

### Expected Output

The application will:
1. Connect to MongoDB and fetch transcript data
2. Display the number of documents retrieved
3. Process transcripts in parallel using multiple workers
4. Show progress for each processed call ID
5. Upload results to Pinecone vector database
6. Confirm successful completion

Example output:
```
Fetched 150 documents from MongoDB.
Processed call ID: 507f1f77bcf86cd799439011
Processed call ID: 507f1f77bcf86cd799439012
...
Results uploaded to Pinecone successfully.
```

## 📁 Project Structure

```
project-02/
├── main.py                 # Main application orchestrator
├── utils.py               # Database utilities (MongoDB & Pinecone)
├── qa_assessment.py       # OpenAI Assistant integration
├── requirements.txt       # Python dependencies
├── README.md             # Project documentation
└── myenv/                # Python virtual environment
    ├── Scripts/          # Virtual environment executables
    ├── Lib/             # Installed packages
    └── pyvenv.cfg       # Virtual environment configuration
```

## 📄 File Descriptions

### `main.py`
The main orchestrator that coordinates the entire pipeline:
- Fetches data from MongoDB using utility functions
- Implements parallel processing using ThreadPoolExecutor
- Manages task queues for efficient workload distribution
- Handles error logging and progress tracking
- Uploads processed results to Pinecone

### `utils.py`
Database connection and utility functions:
- **MongoDB Functions**: Connection setup and data retrieval
- **Pinecone Functions**: Vector database initialization and data upload
- **Configuration**: Database connection strings and API keys

### `qa_assessment.py`
OpenAI Assistant integration module:
- Processes call transcripts using OpenAI Assistant API
- Handles API communication and response parsing
- Returns structured QA analysis results

### `requirements.txt`
Project dependencies:
- `pymongo`: MongoDB Python driver
- `pinecone-client`: Pinecone vector database client
- `openai`: OpenAI API client
- `concurrent-log-handler`: Enhanced logging capabilities
- `flask`: Web framework for potential API endpoints

## 🔄 Data Flow Architecture

The application follows a structured data processing pipeline:

```
MongoDB (Source) → Fetch Transcripts → Queue Distribution →
Parallel Processing (OpenAI Assistant) → Results Collection →
Pinecone Vector DB (Storage)
```

### Detailed Flow:

1. **Data Retrieval**: Connects to MongoDB and fetches call transcripts from the specified collection
2. **Queue Management**: Distributes transcripts across multiple processing queues for parallel execution
3. **AI Processing**: Each worker thread processes transcripts through OpenAI Assistant for QA analysis
4. **Result Aggregation**: Collects processed results from all worker threads
5. **Vector Storage**: Uploads processed results with embeddings to Pinecone for future retrieval

## 🔧 Configuration Details

### Environment Variables (Recommended)
For production deployments, consider using environment variables:

```bash
# MongoDB Configuration
export MONGO_URI="mongodb+srv://username:<EMAIL>/database"
export MONGO_DB_NAME="Insightspro"
export MONGO_COLLECTION_NAME="call_smart_speech_transcribe"

# OpenAI Configuration
export OPENAI_API_KEY="your_openai_api_key"
export OPENAI_ASSISTANT_ID="your_assistant_id"

# Pinecone Configuration
export PINECONE_API_KEY="your_pinecone_api_key"
export PINECONE_ENVIRONMENT="your_environment"
export PINECONE_INDEX_NAME="your_index_name"
```

### Performance Tuning
Adjust the number of worker threads in `main.py` based on your system capabilities:

```python
num_workers = 10  # Adjust based on CPU cores and API rate limits
```

**Recommendations**:
- **CPU-bound tasks**: Set workers = number of CPU cores
- **API rate limits**: Consider OpenAI API rate limits when setting worker count
- **Memory usage**: Monitor memory consumption with large datasets

## 📊 Database Schema

### MongoDB Collection Structure
Expected document structure in the source MongoDB collection:

```json
{
  "_id": "ObjectId or String",
  "text": "Call transcript content...",
  "metadata": {
    "call_duration": "duration_in_seconds",
    "call_date": "ISO_date_string",
    "agent_id": "agent_identifier",
    "customer_id": "customer_identifier"
  }
}
```

### Pinecone Vector Structure
Processed results stored in Pinecone:

```json
{
  "id": "call_id_from_mongodb",
  "values": [0.1, 0.2, ...],  // Embedding vectors
  "metadata": {
    "original_text": "transcript_content",
    "qa_analysis": "processed_insights",
    "processing_timestamp": "ISO_date_string",
    "confidence_score": "analysis_confidence"
  }
}
```

## 🚨 Error Handling

The application includes comprehensive error handling:

### Common Error Scenarios:
1. **MongoDB Connection Issues**: Network connectivity, authentication failures
2. **OpenAI API Errors**: Rate limiting, invalid API keys, service unavailability
3. **Pinecone Upload Failures**: Network issues, index capacity limits
4. **Data Processing Errors**: Malformed transcript data, JSON parsing issues

### Error Recovery:
- Failed transcripts are logged with specific error messages
- Processing continues for remaining transcripts even if individual items fail
- Detailed error logging helps identify and resolve issues quickly

## 🔒 Security Considerations

### API Key Management:
- **Never commit API keys** to version control
- Use environment variables or secure configuration files
- Rotate API keys regularly
- Implement proper access controls for production deployments

### Data Privacy:
- Ensure compliance with data protection regulations
- Consider data encryption for sensitive call transcripts
- Implement proper access logging and audit trails

## 🧪 Testing

### Running Tests
To test the application components:

1. **Test MongoDB Connection**:
   ```python
   from utils import fetch_data_from_mongodb
   data = fetch_data_from_mongodb()
   print(f"Successfully fetched {len(data)} documents")
   ```

2. **Test OpenAI Integration**:
   ```python
   from qa_assessment import assess_call
   result = assess_call("Sample transcript text")
   print(f"QA Analysis: {result}")
   ```

3. **Test Pinecone Upload**:
   ```python
   from utils import upload_to_pinecone
   test_data = [{"_id": "test", "embedding": [0.1, 0.2, 0.3]}]
   upload_to_pinecone(test_data)
   ```

### Performance Testing
Monitor the following metrics:
- **Processing Speed**: Transcripts processed per minute
- **Memory Usage**: RAM consumption during parallel processing
- **API Response Times**: OpenAI and Pinecone API latencies
- **Error Rates**: Failed processing percentage

## 🚀 Deployment

### Local Development
1. Follow the installation steps above
2. Configure your API keys and database connections
3. Run the application using `python main.py`

### Production Deployment
For production environments:

1. **Environment Setup**:
   ```bash
   # Use production-grade Python environment
   python -m venv production_env
   source production_env/bin/activate  # Linux/macOS
   # or
   production_env\Scripts\activate     # Windows
   ```

2. **Security Configuration**:
   - Use environment variables for all sensitive data
   - Implement proper logging and monitoring
   - Set up error alerting and notification systems

3. **Scaling Considerations**:
   - Monitor API rate limits for OpenAI and Pinecone
   - Implement queue management for large datasets
   - Consider distributed processing for very large workloads

### Docker Deployment (Optional)
Create a `Dockerfile` for containerized deployment:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "main.py"]
```

## 📈 Monitoring and Logging

### Application Logs
The application provides detailed logging for:
- MongoDB connection status and document counts
- Processing progress for each transcript
- OpenAI API response times and errors
- Pinecone upload success/failure status
- Overall pipeline performance metrics

### Recommended Monitoring
- **System Resources**: CPU, memory, and network usage
- **API Performance**: Response times and error rates
- **Data Quality**: Processing success rates and data validation
- **Business Metrics**: Total transcripts processed, insights generated

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. MongoDB Connection Errors
```
Error: ServerSelectionTimeoutError
```
**Solution**:
- Verify MongoDB connection string and credentials
- Check network connectivity and firewall settings
- Ensure MongoDB cluster is accessible from your environment

#### 2. OpenAI API Errors
```
Error: RateLimitError or AuthenticationError
```
**Solution**:
- Verify OpenAI API key is valid and has sufficient credits
- Reduce the number of worker threads to respect rate limits
- Implement exponential backoff for API retries

#### 3. Pinecone Upload Failures
```
Error: PineconeException
```
**Solution**:
- Check Pinecone API key and index configuration
- Verify index exists and has sufficient capacity
- Ensure embedding dimensions match index configuration

#### 4. Memory Issues
```
Error: MemoryError or system slowdown
```
**Solution**:
- Reduce the number of worker threads
- Process data in smaller batches
- Monitor system memory usage and optimize accordingly

## 🤝 Contributing

### Development Guidelines
1. **Code Style**: Follow PEP 8 Python style guidelines
2. **Documentation**: Update README for any new features
3. **Testing**: Add tests for new functionality
4. **Error Handling**: Implement proper exception handling
5. **Logging**: Add appropriate logging for debugging

### Pull Request Process
1. Fork the repository
2. Create a feature branch
3. Make your changes with proper documentation
4. Test your changes thoroughly
5. Submit a pull request with detailed description

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For questions, issues, or support:
- Create an issue in the GitHub repository
- Review the troubleshooting section above
- Check the application logs for detailed error information

## 🔮 Future Enhancements

Potential improvements and features:
- **Web Interface**: Flask-based web UI for monitoring and control
- **Batch Processing**: Enhanced batch processing capabilities
- **Real-time Processing**: Stream processing for real-time transcript analysis
- **Advanced Analytics**: Dashboard for processing metrics and insights
- **Multi-language Support**: Support for transcripts in multiple languages
- **Custom Models**: Integration with custom-trained models for specialized analysis

## 📚 Additional Resources

- [MongoDB Python Driver Documentation](https://pymongo.readthedocs.io/)
- [OpenAI API Documentation](https://platform.openai.com/docs/)
- [Pinecone Documentation](https://docs.pinecone.io/)
- [Python Threading Documentation](https://docs.python.org/3/library/threading.html)
- [Flask Documentation](https://flask.palletsprojects.com/)

---

**Note**: This application is designed for processing call transcripts and generating QA insights. Ensure compliance with data privacy regulations and obtain necessary permissions before processing sensitive call data.
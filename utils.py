import os
import sys
import pymongo
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# MongoDB configuration
mongo_uri = os.getenv('MONGO_URI')
mongo_db_name = os.getenv('MONGO_DB_NAME')
mongo_collection_name = os.getenv('MONGO_COLLECTION_NAME')

# Pinecone configuration
pinecone_api_key = os.getenv('PINECONE_API_KEY')
pinecone_index_name = os.getenv('PINECONE_INDEX_NAME')

# Validate required environment variables
required_vars = {
    'MONGO_URI': mongo_uri,
    'MONGO_DB_NAME': mongo_db_name,
    'MONGO_COLLECTION_NAME': mongo_collection_name,
    'PINECONE_API_KEY': pinecone_api_key,
    'PINECONE_INDEX_NAME': pinecone_index_name
}

missing_vars = [var for var, value in required_vars.items() if not value]
if missing_vars:
    print(f"Error: Missing required environment variables: {', '.join(missing_vars)}")
    print("Please check your .env file and ensure all required variables are set.")
    sys.exit(1)

# Initialize MongoDB client
mongo_client = pymongo.MongoClient(mongo_uri)
db = mongo_client[mongo_db_name]
collection = db[mongo_collection_name]

# Initialize Pinecone (using a placeholder for now - will be updated when package is properly installed)
# from pinecone import Pinecone
# pc = Pinecone(api_key=pinecone_api_key)
# index = pc.Index(pinecone_index_name)

# Temporary placeholder - will be updated once Pinecone package is properly installed
pinecone_client = None
index = None

def fetch_data_from_mongodb():
    # Fetch data from MongoDB
    documents = collection.find({}, {'_id': 1, 'text': 1})
    data = [(str(doc['_id']), doc['text']) for doc in documents]
    return data

def upload_to_pinecone(data):
    """
    Upload processed data to Pinecone vector database.
    Note: This function is currently a placeholder until Pinecone package is properly configured.
    """
    if index is None:
        print("Warning: Pinecone client not initialized. Skipping upload to Pinecone.")
        print(f"Would upload {len(data)} documents to Pinecone.")
        return

    # Create embeddings for the text data
    ids = [doc['_id'] for doc in data]
    vectors = [doc['embedding'] for doc in data]

    # Upload to Pinecone
    index.upsert(vectors=zip(ids, vectors))
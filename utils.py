import pymongo
import pinecone

# MongoDB configuration
mongo_uri = "mongodb+srv://dev_users:<EMAIL>/test"
mongo_db_name = "Insightspro"
mongo_collection_name = "call_smart_speech_transcribe"

# Pinecone configuration
pinecone_api_key = "pcsk_2e3oYR_7JPH8McL84WyNVkXUSGN2ppvLQfmJqEr1e4KjEYy8GwLCWdhKkYSydZ3pryAVjv"
pinecone_environment = "PINECONE_ENVIRONMENT"
pinecone_index_name = "chat-sourav"

# Initialize MongoDB client
client = pymongo.MongoClient(mongo_uri)
db = client[mongo_db_name]
collection = db[mongo_collection_name]

# Initialize Pinecone
pinecone.init(api_key=pinecone_api_key, environment=pinecone_environment)
index = pinecone.Index(pinecone_index_name)

def fetch_data_from_mongodb():
    # Fetch data from MongoDB
    documents = collection.find({}, {'_id': 1, 'text': 1})
    data = [(str(doc['_id']), doc['text']) for doc in documents]
    return data

def upload_to_pinecone(data):
    # Create embeddings for the text data
    ids = [doc['_id'] for doc in data]
    vectors = [doc['embedding'] for doc in data]

    # Upload to Pinecone
    index.upsert(vectors=zip(ids, vectors))
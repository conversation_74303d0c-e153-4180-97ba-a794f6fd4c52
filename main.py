import os
from utils import fetch_data_from_mongodb, upload_to_pinecone
from qa_assessment import assess_call
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import json
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def process_transcript(call_id, transcript_queue, results_queue):
    while not transcript_queue.empty():
        try:
            call_id, transcript = transcript_queue.get_nowait()
            result = assess_call(transcript)
            result_dict = json.loads(result)
            result_dict["_id"] = call_id
            results_queue.put(result_dict)
            print(f"Processed call ID: {call_id}")
        except queue.Empty:
            break
        except Exception as e:
            print(f"Error processing call ID {call_id}: {e}")
            transcript_queue.task_done()

def main():
    try:
        # Fetch data from MongoDB
        data = fetch_data_from_mongodb()
        print(f"Fetched {len(data)} documents from MongoDB.")

        # Prepare queues
        transcript_queue = queue.Queue()
        results_queue = queue.Queue()

        for call_id, transcript in data:
            transcript_queue.put((call_id, transcript))

        # Number of workers for parallel processing
        num_workers = int(os.getenv('NUM_WORKERS', 10))  # Default to 10 if not set

        # Process transcripts in parallel
        results = []
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [executor.submit(process_transcript, None, transcript_queue, results_queue) for _ in range(num_workers)]
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"Error in worker: {e}")

        # Collect results from the queue
        while not results_queue.empty():
            results.append(results_queue.get_nowait())

        # Upload results to Pinecone
        upload_to_pinecone(results)
        print("Results uploaded to Pinecone successfully.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
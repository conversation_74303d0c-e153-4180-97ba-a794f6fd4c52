# QA Pipeline Web Console

## Overview

The QA Pipeline Web Console is a comprehensive Flask-based web application that provides a user-friendly interface for managing and monitoring the Call Transcript QA Analysis Pipeline. It offers real-time monitoring, user management, processing controls, and detailed analytics.

## Features

### 🔐 Authentication & Security
- Secure user authentication with bcrypt password hashing
- Role-based access control (Admin/User roles)
- Session management with CSRF protection
- Password strength validation
- User registration (admin-only)

### 📊 Dashboard & Monitoring
- Real-time system status monitoring
- Connection health checks for all services (MongoDB, MySQL, Pinecone, OpenAI)
- Processing statistics and analytics
- User usage tracking and limits
- System resource monitoring (CPU, memory, disk)

### 🎯 Transcript Processing
- Web-based transcript processing interface
- Real-time processing status updates
- Usage limit enforcement
- Processing history and logs
- Result viewing and analysis

### 👥 User Management (Admin)
- User creation and management
- Role assignment and permissions
- Usage limit configuration
- User activity monitoring
- Bulk user operations

### 📈 Analytics & Reporting
- Processing performance metrics
- User activity analytics
- System health trends
- Data export capabilities
- Custom reporting dashboards

## Installation & Setup

### 1. Prerequisites
- Python 3.8+
- MySQL database
- MongoDB instance
- OpenAI API access
- Pinecone account

### 2. Install Dependencies
```bash
# Activate virtual environment
myenv\Scripts\activate

# Install required packages
pip install -r requirements.txt
```

### 3. Environment Configuration
Copy and configure the environment file:
```bash
copy .env.example .env
```

Edit `.env` with your configuration:
```bash
# Web Console Configuration
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/qa_pipeline_db
SECRET_KEY=your-secret-key-change-this-in-production
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
WEB_HOST=127.0.0.1
WEB_PORT=5000
FLASK_ENV=development
FLASK_DEBUG=True

# Existing Pipeline Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/database
MONGO_DB_NAME=your_database_name
MONGO_COLLECTION_NAME=your_collection_name
OPENAI_API_KEY=your_openai_api_key
OPENAI_ASSISTANT_ID=your_assistant_id
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_INDEX_NAME=your_index_name
NUM_WORKERS=10
```

### 4. Database Setup
Initialize the database:
```bash
python init_db.py init
```

Add sample data (optional):
```bash
python init_db.py sample
```

### 5. Launch Web Console
```bash
python run_web_console.py
```

Or run directly:
```bash
python app.py
```

## Usage Guide

### First Login
1. Open your browser to `http://127.0.0.1:5000`
2. Login with admin credentials:
   - Username: `admin` (or your configured admin username)
   - Password: `admin123` (or your configured admin password)

### Dashboard Overview
The main dashboard provides:
- System status indicators
- Usage statistics
- Quick action buttons
- Recent activity feed

### Processing Transcripts
1. Navigate to "Process Transcript"
2. Paste your call transcript text
3. Select processing priority
4. Click "Process Transcript"
5. View results when processing completes

### User Management (Admin)
1. Go to "Admin Dashboard" → "User Management"
2. Create new users with "Add User"
3. Set role, daily/monthly limits
4. Manage existing users (edit, deactivate, reset limits)

### Monitoring & Analytics
- **System Info**: View real-time system metrics
- **Processing Logs**: Monitor all transcript processing
- **Analytics**: View usage trends and performance metrics

## API Endpoints

### Health & Status
- `GET /api/health` - General health check
- `GET /api/connections/mongodb` - MongoDB connection status
- `GET /api/connections/mysql` - MySQL connection status
- `GET /api/connections/pinecone` - Pinecone connection status
- `GET /api/connections/openai` - OpenAI configuration status

### System Metrics
- `GET /api/system/metrics` - Current system metrics
- `GET /api/processing/stats` - Processing statistics
- `GET /api/users/stats` - User statistics (admin only)

### Data Export
- `GET /admin/export/logs` - Export processing logs as JSON

## Architecture

### Application Structure
```
project-02/
├── app.py                      # Main Flask application
├── run_web_console.py         # Web console launcher
├── init_db.py                 # Database initialization
├── web_console/               # Web console package
│   ├── __init__.py
│   ├── models.py              # Database models
│   ├── auth.py                # Authentication blueprint
│   ├── dashboard.py           # Dashboard blueprint
│   ├── api.py                 # API blueprint
│   └── admin.py               # Admin blueprint
├── templates/                 # HTML templates
│   ├── base.html              # Base template
│   ├── auth/                  # Authentication templates
│   ├── dashboard/             # Dashboard templates
│   ├── admin/                 # Admin templates
│   └── errors/                # Error page templates
├── static/                    # Static files (CSS, JS, images)
└── migrations/                # Database migrations
```

### Database Schema
- **user_profiles**: User accounts and settings
- **processing_logs**: Transcript processing history
- **system_metrics**: System performance data

### Security Features
- Password hashing with bcrypt
- CSRF protection on all forms
- Session-based authentication
- Role-based access control
- Input validation and sanitization

## Configuration Options

### Environment Variables
| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | MySQL connection string | Required |
| `SECRET_KEY` | Flask secret key | Required |
| `ADMIN_USERNAME` | Default admin username | `admin` |
| `ADMIN_PASSWORD` | Default admin password | `admin123` |
| `WEB_HOST` | Web server host | `127.0.0.1` |
| `WEB_PORT` | Web server port | `5000` |
| `FLASK_ENV` | Flask environment | `development` |
| `FLASK_DEBUG` | Enable debug mode | `True` |

### User Roles
- **Admin**: Full access to all features, user management, system configuration
- **User**: Access to transcript processing, personal logs, profile management

### Usage Limits
- Daily processing limits per user
- Monthly processing limits per user
- Configurable by administrators
- Automatic reset at midnight/month start

## Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Check database configuration
python init_db.py check

# Reset database if needed
python init_db.py reset
```

#### Missing Dependencies
```bash
# Install all requirements
pip install -r requirements.txt

# Check specific package
python -c "import flask; print('Flask installed')"
```

#### Permission Errors
- Ensure user has correct role assignment
- Check if user account is active
- Verify usage limits haven't been exceeded

#### Performance Issues
- Monitor system resources in "System Info"
- Check processing logs for errors
- Adjust `NUM_WORKERS` in environment

### Logs & Debugging
- Application logs are displayed in console
- Enable debug mode with `FLASK_DEBUG=True`
- Check processing logs in web interface
- Monitor system metrics for performance issues

## Development

### Adding New Features
1. Create new blueprint in `web_console/`
2. Add routes and templates
3. Register blueprint in `app.py`
4. Update navigation in `base.html`

### Database Changes
1. Modify models in `web_console/models.py`
2. Create migration: `flask db migrate -m "description"`
3. Apply migration: `flask db upgrade`

### Testing
- Use sample data: `python init_db.py sample`
- Test API endpoints with curl or Postman
- Check all user roles and permissions

## Security Considerations

### Production Deployment
- Change default admin password
- Use strong secret key
- Enable HTTPS
- Configure proper database permissions
- Set up proper logging and monitoring
- Regular security updates

### Best Practices
- Regular password rotation
- Monitor user activity
- Backup database regularly
- Keep dependencies updated
- Use environment variables for secrets

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review application logs
3. Verify environment configuration
4. Check database connectivity

## License

This project is part of the Call Transcript QA Analysis Pipeline and follows the same licensing terms.
